:root {
  /* Modern color palette with glassmorphism support */
  --primary-color: #00D4AA;
  --primary-light: #26E5CC;
  --primary-dark: #00A896;
  --primary-gradient: linear-gradient(135deg, #00D4AA 0%, #00A896 100%);
  --accent-color: #FFB84D;
  --accent-gradient: linear-gradient(135deg, #FFB84D 0%, #F9A826 100%);
  --success-color: #4ECDC4;
  --warning-color: #FFB84D;
  --error-color: #FF6B6B;

  /* Modern neutral colors with depth - even darker and more transparent */
  --dark-bg: #050810;
  --dark-surface: #0A0E14;
  --medium-dark: #0F1419;
  --light-surface: #1A1F29;
  --glass-surface: rgba(10, 14, 20, 0.4);
  --glass-surface-light: rgba(15, 20, 25, 0.3);
  --glass-surface-dark: rgba(5, 8, 16, 0.6);

  /* Text colors with better contrast */
  --light-text: #F7FAFC;
  --medium-text: #E2E8F0;
  --muted-text: #A0AEC0;
  --subtle-text: #718096;

  /* Modern glassmorphism properties */
  --border-radius-xs: 6px;
  --border-radius-sm: 8px;
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  --border-radius-full: 9999px;

  /* Enhanced shadow system */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(0, 212, 170, 0.3);
  --shadow-glow-accent: 0 0 20px rgba(255, 184, 77, 0.3);

  /* Smooth transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);

  /* Glassmorphism effects - enhanced */
  --glass-blur: blur(20px);
  --glass-blur-strong: blur(32px);
  --glass-border: 1px solid rgba(255, 255, 255, 0.08);
  --glass-border-strong: 1px solid rgba(255, 255, 255, 0.15);

  /* Component dimensions - even more compact */
  --header-height: 40px;
  --action-bar-height: 36px;
  --tabs-height: 32px;
  --status-height: 28px;
  --content-padding: 12px;

  /* Modern spacing scale */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
  --space-3xl: 32px;
}

/* Base Styles & Resets */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100vh;
  overflow: hidden;
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--light-text);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background: linear-gradient(135deg, var(--dark-bg) 0%, #030508 100%);
  padding: 0;
  margin: 0;
  user-select: none;
  position: relative;
}

/* Animated background pattern */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 184, 77, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Modern Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-surface-dark);
  backdrop-filter: var(--glass-blur-strong);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  transition: opacity var(--transition);
  pointer-events: none;
}

.loading-overlay.visible {
  opacity: 1;
  pointer-events: auto;
}

.loading-overlay i {
  font-size: 56px;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-xl);
  filter: drop-shadow(0 0 10px rgba(0, 212, 170, 0.3));
}

.loading-message {
  color: var(--light-text);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: var(--space-sm);
  letter-spacing: -0.025em;
}

.sub-message {
  color: var(--medium-text);
  font-size: 15px;
  font-weight: 400;
}

/* Modern Application Container - more transparent and compact */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--glass-surface);
  backdrop-filter: var(--glass-blur-strong);
  border: var(--glass-border);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-xl), 0 0 40px rgba(0, 0, 0, 0.5);
  position: relative;
}

/* Modern Header Section - more compact */
.app-header {
  height: var(--header-height);
  background: var(--glass-surface-light);
  backdrop-filter: var(--glass-blur-strong);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--space-lg);
  -webkit-app-region: drag;
  position: relative;
  border-bottom: var(--glass-border);
  box-shadow: var(--shadow-sm);
}

.logo-area {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  flex: 1;
}

.logo-icon {
  font-size: 28px;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 8px rgba(0, 212, 170, 0.3));
}

.app-header h3 {
  font-size: 19px;
  font-weight: 600;
  color: var(--light-text);
  letter-spacing: -0.025em;
}

/* Modern Shortcut hints styling - more compact */
.shortcut-hints {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-left: var(--space-md);
}

.shortcut-hints span {
  background: var(--glass-surface-light);
  border: var(--glass-border);
  border-radius: var(--border-radius-full);
  padding: var(--space-xs) var(--space-sm);
  font-size: 10px;
  font-weight: 500;
  color: var(--medium-text);
  backdrop-filter: var(--glass-blur);
  transition: all var(--transition-fast);
  white-space: nowrap;
  letter-spacing: 0.025em;
}

.shortcut-hints span:hover {
  background: var(--glass-surface);
  color: var(--light-text);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-glow);
}

.shortcut-hints i {
  color: var(--primary-color);
  margin-right: var(--space-xs);
  font-size: 10px;
}

.header-controls {
  display: flex;
  gap: var(--space-sm);
  -webkit-app-region: no-drag;
}

.minimize-btn, .close-btn {
  width: 28px;
  height: 28px;
  border-radius: var(--border-radius);
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--glass-surface-light);
  border: var(--glass-border);
  color: var(--muted-text);
  cursor: pointer;
  transition: all var(--transition-fast);
  backdrop-filter: var(--glass-blur);
}

.minimize-btn:hover {
  background: var(--glass-surface);
  color: var(--light-text);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.close-btn:hover {
  background: var(--error-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
  border-color: var(--error-color);
}

/* Quick Action Bar */
.quick-action-bar {
  height: var(--action-bar-height);
  background: var(--medium-dark);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.action-button-group {
  display: flex;
  gap: 12px;
}

.action-button {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  color: var(--light-text);
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: var(--transition-fast);
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.action-button:active {
  transform: translateY(0);
}

.context-status {
  font-size: 14px;
  font-weight: 500;
  color: var(--muted-text);
}

/* Simple Tab Navigation - even more compact */
.tabs-nav {
  height: var(--tabs-height);
  display: flex;
  background: var(--glass-surface-light);
  padding: 4px 8px;
  border-bottom: 1px solid rgba(0, 212, 170, 0.1);
  gap: 4px;
}

/* Remove instructional text - tabs are now visually obvious */

.tab {
  padding: 4px 8px;
  border-radius: var(--border-radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: var(--transition-fast);
  color: var(--muted-text);
  font-size: 11px;
  font-weight: 500;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  min-width: 70px;
}

.tab:hover {
  background: rgba(0, 212, 170, 0.1);
  color: var(--light-text);
  border-color: rgba(0, 212, 170, 0.3);
}

.tab.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.tab i {
  font-size: 12px;
}

.tab span {
  font-weight: 500;
}

/* Modern Main Content Area */
.content-area {
  flex: 1;
  overflow: hidden;
  position: relative;
  background: var(--glass-surface-light);
  backdrop-filter: var(--glass-blur);
}

.tab-content {
  display: none;
  height: 100%;
  overflow: hidden;
}

.tab-content.active {
  display: flex;
  flex-direction: column;
}

/* Modern Chat Interface */
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-messages-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-header, .audio-header, .shortcuts-header {
  padding: var(--space-sm) var(--space-md);
  background: var(--glass-surface);
  backdrop-filter: var(--glass-blur);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: var(--glass-border);
  box-shadow: var(--shadow-xs);
}

.chat-title, .audio-title, .shortcuts-title {
  font-weight: 600;
  font-size: 12px;
  color: var(--light-text);
  letter-spacing: -0.025em;
}

.chat-status, .audio-status {
  font-size: 13px;
  font-weight: 500;
  color: var(--muted-text);
  padding: var(--space-xs) var(--space-md);
  background: var(--glass-surface-light);
  border-radius: var(--border-radius-full);
  border: var(--glass-border);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-md);
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 212, 170, 0.3) transparent;
  min-height: 0; /* Allow shrinking */
}

.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
  border-radius: var(--border-radius-sm);
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--glass-surface);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--glass-border);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
  box-shadow: var(--shadow-glow);
}

/* Modern Message Styles - even more compact */
.message {
  max-width: 85%;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--border-radius-lg);
  position: relative;
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  transition: all var(--transition-slow);
  backdrop-filter: var(--glass-blur);
  border: var(--glass-border);
  box-shadow: var(--shadow);
}

.message.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.message-content {
  font-size: 12px;
  line-height: 1.4;
  font-weight: 400;
  letter-spacing: -0.01em;
}

.message-time {
  font-size: 12px;
  opacity: 0.6;
  margin-top: var(--space-sm);
  text-align: right;
  font-weight: 500;
}

.user-message {
  align-self: flex-end;
  background: var(--primary-gradient);
  color: white;
  border-bottom-right-radius: var(--space-sm);
  box-shadow: var(--shadow-glow);
}

.user-message .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.ai-message {
  align-self: flex-start;
  background: var(--glass-surface);
  color: var(--light-text);
  border-bottom-left-radius: var(--space-sm);
}

.ai-message .message-time {
  color: var(--muted-text);
}

.message-image {
  max-width: 100%;
  border-radius: var(--border-radius);
  margin-bottom: var(--space-sm);
  transition: all var(--transition);
  box-shadow: var(--shadow);
}

.message-image:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-lg);
}

/* Modern Chat Input Container - even more compact */
.chat-input-container {
  padding: var(--space-md);
  background: var(--glass-surface);
  backdrop-filter: var(--glass-blur);
  border-top: var(--glass-border);
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  box-shadow: var(--shadow-sm);
}

.chat-toolbox {
  display: flex;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.input-row {
  display: flex;
  gap: var(--space-md);
  align-items: flex-end;
}

.chat-input {
  flex: 1;
  background: var(--glass-surface-light);
  border: var(--glass-border);
  border-radius: var(--border-radius-lg);
  color: var(--light-text);
  padding: var(--space-sm);
  outline: none;
  transition: all var(--transition);
  font-size: 12px;
  font-family: inherit;
  line-height: 1.3;
  backdrop-filter: var(--glass-blur);
  resize: none;
}

.chat-input::placeholder {
  color: var(--muted-text);
  font-weight: 400;
}

.chat-input:focus {
  border-color: var(--primary-color);
  background: var(--glass-surface);
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

.send-button {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--border-radius-lg);
  color: white;
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow-glow);
  position: relative;
  overflow: hidden;
}

.send-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.send-button:hover::before {
  opacity: 1;
}

.send-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-glow), var(--shadow-lg);
}

.send-button:active {
  transform: translateY(-1px) scale(1.02);
}

.chat-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.action-btn {
  padding: var(--space-sm) var(--space-lg);
  background: var(--glass-surface-light);
  border: var(--glass-border);
  border-radius: var(--border-radius-full);
  color: var(--muted-text);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  backdrop-filter: var(--glass-blur);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-surface);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.action-btn:hover::before {
  opacity: 1;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--light-text);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.action-btn i {
  position: relative;
  z-index: 1;
}

.action-btn span {
  position: relative;
  z-index: 1;
}

/* Audio Interface */
.audio-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.audio-controls {
  padding: 16px;
  display: flex;
  gap: 16px;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.recording-btn {
  padding: 12px 24px;
  border-radius: var(--border-radius);
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

#startRecordingBtn {
  background: var(--success-color);
  color: white;
}

#startRecordingBtn:hover {
  background: #3d8b40;
}

#stopRecordingBtn {
  background: var(--error-color);
  color: white;
}

#stopRecordingBtn:hover {
  background: #c62828;
}

.recording-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.audio-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.audio-info {
  font-size: 14px;
  line-height: 1.6;
  color: var(--light-text);
  white-space: pre-wrap;
}

.info-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: var(--muted-text);
  height: 100%;
  text-align: center;
  padding: 48px 0;
}

.info-placeholder i {
  font-size: 32px;
  margin-bottom: 16px;
}

/* Shortcuts Interface */
.shortcuts-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.shortcuts-content {
  padding: 16px;
  overflow-y: auto;
}

.shortcuts-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.shortcuts-table th, .shortcuts-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.shortcuts-table th {
  font-weight: 500;
  color: var(--light-text);
  background: var(--medium-dark);
}

.shortcuts-table td {
  color: var(--medium-text);
}

.shortcut-key {
  display: inline-block;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-sm);
  font-family: monospace;
  margin-right: 4px;
}

/* Modern Status Bar - more compact */
.status-bar {
  height: var(--status-height);
  background: var(--glass-surface);
  backdrop-filter: var(--glass-blur);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--space-lg);
  border-top: var(--glass-border);
  color: var(--muted-text);
  font-size: 11px;
  font-weight: 500;
  box-shadow: var(--shadow-xs);
}

.status-info {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.status-info i {
  color: var(--primary-color);
  font-size: 12px;
}

.app-version {
  background: var(--glass-surface-light);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--border-radius-full);
  border: var(--glass-border);
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.05em;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(22, 30, 40, 0.9);
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-overlay i {
  font-size: 48px;
  margin-bottom: 24px;
  color: var(--primary-color);
  animation: spin 1.2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  font-size: 24px;
  margin-bottom: 16px;
  color: var(--light-text);
}

.sub-message {
  font-size: 16px;
  color: var(--muted-text);
}

/* Custom Dropdown Styling */
.custom-dropdown {
  display: none;
}

.option-group {
  display: flex;
  gap: var(--space-sm);
  align-items: center;
  margin-bottom: var(--space-xs);
}

.option-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--medium-text);
  margin-right: var(--space-xs);
  letter-spacing: -0.01em;
}

.option-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.option-btn {
  background: var(--glass-surface-light);
  border: var(--glass-border);
  border-radius: var(--border-radius-full);
  color: var(--light-text);
  font-size: 11px;
  font-weight: 500;
  padding: var(--space-xs) var(--space-md);
  cursor: pointer;
  transition: all var(--transition);
  backdrop-filter: var(--glass-blur);
  position: relative;
  overflow: hidden;
  letter-spacing: -0.01em;
}

.option-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-surface);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.option-btn:hover::before {
  opacity: 1;
}

.option-btn:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.option-btn.selected {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

.option-btn.selected::before {
  opacity: 0;
}

.option-btn span {
  position: relative;
  z-index: 1;
}

/* Hide the actual radio buttons */
.option-radio {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* Enhanced Markdown Content Styles */
.markdown-content {
  line-height: 1.6;
}

.markdown-content pre {
  background: var(--glass-surface-dark);
  backdrop-filter: var(--glass-blur);
  border: var(--glass-border);
  padding: var(--space-lg);
  border-radius: var(--border-radius);
  overflow-x: auto;
  margin: var(--space-lg) 0;
  position: relative;
  box-shadow: var(--shadow);
}

.markdown-content pre:hover .copy-code-btn {
  opacity: 1;
}

.copy-code-btn {
  position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  background: var(--glass-surface);
  border: var(--glass-border);
  border-radius: var(--border-radius-sm);
  color: var(--muted-text);
  padding: var(--space-xs) var(--space-sm);
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  opacity: 0;
  transition: all var(--transition);
  backdrop-filter: var(--glass-blur);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.copy-code-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-glow);
}

.copy-code-btn.copied {
  background: var(--success-color);
  color: white;
  opacity: 1;
}

.markdown-content code {
  background: var(--glass-surface);
  border: var(--glass-border);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-xs);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  font-weight: 500;
}

.markdown-content p {
  margin-bottom: 12px;
}

.markdown-content ul, .markdown-content ol {
  margin-left: 24px;
  margin-bottom: 16px;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3,
.markdown-content h4, .markdown-content h5, .markdown-content h6 {
  margin-top: 20px;
  margin-bottom: 12px;
  color: var(--light-text);
}

/* Mermaid diagram styles */
.mermaid {
  background: rgba(0, 0, 0, 0.2);
  padding: 16px;
  border-radius: var(--border-radius);
  margin: 16px 0;
  overflow-x: auto;
}

.mermaid svg {
  max-width: 100%;
  height: auto;
  fill: var(--light-text);
  stroke: var(--light-text);
  background: transparent;
}

.mermaid svg text {
  fill: var(--light-text);
}

.mermaid svg path, .mermaid svg line {
  stroke: var(--light-text);
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  :root {
    --header-height: 56px;
    --tabs-height: 48px;
    --content-padding: 16px;
  }

  .app-header {
    padding: 0 var(--space-lg);
  }

  .shortcut-hints {
    display: none; /* Hide on mobile for space */
  }

  .chat-input-container {
    padding: var(--space-lg);
  }

  .message {
    max-width: 92%;
    padding: var(--space-md) var(--space-lg);
  }

  .tab {
    padding: var(--space-sm) var(--space-md);
    font-size: 13px;
  }

  .tab i {
    font-size: 14px;
  }

  .option-buttons {
    gap: var(--space-xs);
  }

  .option-btn {
    padding: var(--space-xs) var(--space-md);
    font-size: 12px;
  }

  .chat-toolbox {
    flex-direction: column;
    gap: var(--space-md);
  }

  .floating-notification {
    top: var(--space-md);
    right: var(--space-md);
    left: var(--space-md);
    max-width: none;
  }
}

@media (max-width: 480px) {
  .tabs-nav {
    padding: var(--space-xs) var(--space-md);
    gap: var(--space-xs);
  }

  .tab {
    padding: var(--space-xs) var(--space-sm);
    min-width: auto;
  }

  .tab span {
    display: none; /* Show only icons on very small screens */
  }

  .message {
    max-width: 95%;
  }

  .chat-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
}

/* Screen sharing specific styles - hide dropdowns during screen sharing */
@media (display-mode: capture), screen and (display-mode: capture) {
  .custom-dropdown, .action-button {
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
  }
}

/* Mozilla Firefox-specific styles */
@-moz-document url-prefix() {
  .chat-messages {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
  }
}

/* Enhanced visual polish for compact design */
.app-container {
  /* Add subtle inner glow for depth */
  box-shadow:
    var(--shadow-xl),
    0 0 40px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Improved hover states for better interactivity */
.tab:hover {
  background: rgba(0, 212, 170, 0.08);
  color: var(--light-text);
  border-color: rgba(0, 212, 170, 0.2);
  transform: translateY(-1px);
}

/* Enhanced focus states for accessibility */
.chat-input:focus {
  border-color: var(--primary-color);
  background: var(--glass-surface);
  box-shadow:
    var(--shadow-glow),
    0 0 0 3px rgba(0, 212, 170, 0.1);
  transform: translateY(-1px);
}

/* Smooth transitions for dynamic sizing */
.chat-messages-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Better visual hierarchy with subtle gradients */
.chat-header {
  background: linear-gradient(135deg, var(--glass-surface) 0%, var(--glass-surface-light) 100%);
}

.status-bar {
  background: linear-gradient(135deg, var(--glass-surface-light) 0%, var(--glass-surface) 100%);
}