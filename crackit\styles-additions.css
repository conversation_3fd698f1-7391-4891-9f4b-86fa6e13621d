.processing-message {
  margin: var(--space-sm) 0;
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--border-radius-lg);
  background: var(--glass-surface);
  backdrop-filter: var(--glass-blur);
  border: var(--glass-border);
  border-left: 3px solid var(--primary-color);
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--space-md);
  min-width: 180px;
  position: relative;
  box-shadow: var(--shadow);
}

.processing-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 212, 170, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 0.8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  flex-shrink: 0;
  filter: drop-shadow(0 0 4px rgba(0, 212, 170, 0.3));
}

.processing-status {
  color: var(--medium-text);
  font-size: 14px;
  font-weight: 500;
  flex-grow: 1;
  letter-spacing: -0.01em;
}

/* Core spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Status indicators */
.status-info { color: var(--medium-text); }
.status-success { color: var(--success-color); }
.status-error { color: var(--error-color); }
.status-warning { color: var(--warning-color); }

/* Modern Loading overlay */
#loadingOverlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--glass-surface-dark);
  backdrop-filter: var(--glass-blur-strong);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

#loadingOverlay.visible {
  display: flex;
  opacity: 1;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  position: relative;
  margin-bottom: var(--space-xl);
}

.loading-spinner::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid rgba(0, 212, 170, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  filter: drop-shadow(0 0 8px rgba(0, 212, 170, 0.4));
}

.loading-spinner::after {
  content: '';
  position: absolute;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border: 2px solid transparent;
  border-top-color: var(--primary-light);
  border-radius: 50%;
  animation: spin 0.6s ease-in-out infinite reverse;
  opacity: 0.7;
}

.loading-text {
  margin-top: var(--space-sm);
  font-size: 14px;
  font-weight: 500;
  color: var(--light-text);
  text-align: center;
  letter-spacing: -0.01em;
}

/* Modern Floating notification */
.floating-notification {
  position: fixed;
  top: var(--space-xl);
  right: var(--space-xl);
  padding: var(--space-lg) var(--space-xl);
  background: var(--glass-surface);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  color: var(--light-text);
  z-index: 2000;
  display: flex;
  align-items: center;
  opacity: 0;
  transform: translateX(100%) scale(0.9);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 320px;
  border: var(--glass-border);
  border-left: 3px solid var(--primary-color);
  font-weight: 500;
  font-size: 14px;
}

.floating-notification.show {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.floating-notification i {
  margin-right: var(--space-md);
  font-size: 18px;
  filter: drop-shadow(0 0 4px currentColor);
}

.floating-notification.notification-success {
  border-left-color: var(--success-color);
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.2), var(--shadow-lg);
}

.floating-notification.notification-success i {
  color: var(--success-color);
}

.floating-notification.notification-error {
  border-left-color: var(--error-color);
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.2), var(--shadow-lg);
}

.floating-notification.notification-error i {
  color: var(--error-color);
}

.floating-notification.notification-warning {
  border-left-color: var(--warning-color);
  box-shadow: 0 0 20px rgba(255, 184, 77, 0.2), var(--shadow-lg);
}

.floating-notification.notification-warning i {
  color: var(--warning-color);
}

/* Modern Textarea styling - even more compact */
textarea.chat-input {
  resize: none;
  overflow-y: hidden;
  min-height: 28px;
  height: 28px;
  max-height: 100px;
  padding: var(--space-sm);
  line-height: 1.3;
  transition: all var(--transition);
  font-family: inherit;
  font-weight: 400;
  letter-spacing: -0.01em;
}

/* Enhanced auto-resize textarea on input */
textarea.chat-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

/* Smooth scrollbar for textarea */
textarea.chat-input::-webkit-scrollbar {
  width: 6px;
}

textarea.chat-input::-webkit-scrollbar-track {
  background: transparent;
}

textarea.chat-input::-webkit-scrollbar-thumb {
  background: var(--glass-surface);
  border-radius: var(--border-radius-sm);
}

textarea.chat-input::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Enhanced focus states for accessibility */
.tab:focus-visible,
.option-btn:focus-visible,
.action-btn:focus-visible,
.send-button:focus-visible,
.chat-input:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Smooth animations for better UX */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Enhanced button hover effects */
.send-button:hover {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Improved loading states */
.loading-state {
  pointer-events: none;
  opacity: 0.7;
  filter: blur(1px);
  transition: all var(--transition);
}

/* Modern selection styles */
::selection {
  background: rgba(0, 212, 170, 0.3);
  color: var(--light-text);
}

::-moz-selection {
  background: rgba(0, 212, 170, 0.3);
  color: var(--light-text);
}

/* Enhanced glass effect for better depth */
.glass-effect {
  background: var(--glass-surface);
  backdrop-filter: var(--glass-blur);
  border: var(--glass-border);
  box-shadow: var(--shadow);
}

/* Improved status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-full);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-indicator.online {
  background: rgba(78, 205, 196, 0.2);
  color: var(--success-color);
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.status-indicator.offline {
  background: rgba(255, 107, 107, 0.2);
  color: var(--error-color);
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.status-indicator.processing {
  background: rgba(255, 184, 77, 0.2);
  color: var(--warning-color);
  border: 1px solid rgba(255, 184, 77, 0.3);
}

/* Simple tab badge styling */
.tab-switch-hint {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--accent-color);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Simplified tab styling - removed complex effects */

/* Code block enhancements */
.code-block-container {
  position: relative;
  margin: var(--space-lg) 0;
}

.code-block-header {
  background: var(--glass-surface);
  border: var(--glass-border);
  border-bottom: none;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  padding: var(--space-sm) var(--space-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--muted-text);
  font-weight: 600;
}

.code-language-badge {
  background: var(--primary-gradient);
  color: white;
  padding: 2px 8px;
  border-radius: var(--border-radius-full);
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Enhanced copy button states */
.copy-code-btn.copying {
  background: var(--warning-color);
  color: white;
  opacity: 1;
  transform: translateY(-1px) scale(0.95);
}

.copy-code-btn.failed {
  background: var(--error-color);
  color: white;
  opacity: 1;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Onscreen keyboard paste button enhancements */
button[data-key="Paste"] {
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
}

button[data-key="Paste"]::before {
  content: '📋 ';
  font-size: 12px;
  margin-right: 2px;
}

button[data-key="Paste"].pasting {
  background: var(--success-color) !important;
  color: white !important;
  transform: scale(0.95);
  transition: all 0.2s ease;
}

button[data-key="Paste"].paste-error {
  background: var(--error-color) !important;
  color: white !important;
  animation: shake 0.3s ease-in-out;
}

/* Better visual feedback for keyboard interactions */
button[data-key]:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

button[data-key].pressed {
  background: var(--primary-color) !important;
  color: white !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Ensure all keyboard buttons have consistent styling */
button[data-key] {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  overflow: hidden !important;
}