<!DOCTYPE html>
<html>
<head>
  <title>mDNSResponder</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/vs2015.min.css">
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="styles-additions.css">
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
</head>
<body>
  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loadingOverlay">
    <i class="fas fa-spinner fa-spin"></i>
    <div class="loading-message">Processing...</div>
    <div class="sub-message">Please wait a moment.</div>
  </div>  <div class="app-container">
    <!-- Header Section -->
    <header class="app-header">
      <div class="logo-area">
        <div class="shortcut-hints">
          <span><i class="fas fa-keyboard"></i> Toggle: Ctrl/Cmd+Shift+T</span>
          <span><i class="fas fa-arrows-alt"></i> Resize: Ctrl+Shift + arrow keys</span>
          <span><i class="fas fa-camera"></i> Screenshot: Ctrl+Shift+C</span>
          <span><i class="fas fa-cogs"></i> Process: Ctrl+Shift+P</span>
        </div>
      </div>
      <div class="header-controls">
        <button class="minimize-btn" id="forceTopBtn" title="Force Always On Top">
          <i class="fas fa-arrow-up"></i>
        </button>
        <button class="close-btn" id="closeBtn" title="Close Window">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </header>

    <!-- Quick Action Bar -->
    <!-- <div class="quick-action-bar">
      <div class="action-button-group">
        <button class="action-button" id="captureBtn" title="Capture Screen (Ctrl+Shift+C)">
          <i class="fas fa-camera"></i> Capture
        </button>
        <button class="action-button" id="processBtn" title="Process Image (Ctrl+Shift+P)">
          <i class="fas fa-cogs"></i> Process
        </button>
      </div>
      <div class="context-status">
        <span id="status">Ready</span>
      </div>
    </div>
     -->
    <!-- Enhanced Tabs Navigation with Clear Sections -->
    <nav class="tabs-nav">
      <div class="tab active" data-tab="assistant">
        <i class="fas fa-comment-dots"></i> <span>Coding</span>
      </div>
      <div class="tab" data-tab="general">
        <i class="fas fa-comments"></i> <span>General Chat</span>
        <span class="tab-switch-hint">New</span>
      </div>
      <div class="tab" data-tab="system-design">
        <i class="fas fa-project-diagram"></i> <span>System Design</span>
      </div>
      <div class="tab" data-tab="audio">
        <i class="fas fa-microphone"></i> <span>Audio</span>
      </div>
      <div class="tab" data-tab="shortcuts">
        <i class="fas fa-keyboard"></i> <span>Shortcuts</span>
      </div>
    </nav>

    <!-- Main Content Area -->
    <main class="content-area">
      <!-- Assistant Tab -->
      <div class="tab-content active" id="assistant-tab">
        <div class="chat-interface">
          <!-- Chat Messages Area -->
          <div class="chat-messages-container">
            <div class="chat-header">
              <span class="chat-title">AI Assistant</span>
              <span id="chat-status" class="chat-status">Ready</span>
            </div>
            <div class="chat-messages" id="chat-messages">
              <div class="message ai-message visible">
                <div class="message-content">
                  Hello! I'm here to help. You can chat with me or capture a screenshot (Ctrl+Shift+C, then Ctrl+Shift+P to process). Select a model to get started!
                </div>
                <div class="message-time">Now</div>
              </div>
            </div>
          </div>

          <!-- Chat Input Area -->
          <div class="chat-input-container">
            <div class="chat-toolbox">
              <!-- Language Option Group -->
              <div class="option-group">
                <div class="option-label">Language:</div>
                <div class="option-buttons" id="language-options">
                  <label class="option-btn selected">
                    <input type="radio" name="language" value="Python" class="option-radio" checked>
                    Python
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="language" value="JavaScript" class="option-radio">
                    JavaScript
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="language" value="Java" class="option-radio">
                    Java
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="language" value="C++" class="option-radio">
                    C++
                  </label>
                </div>
              </div>

              <!-- Model Option Group -->
              <div class="option-group">
                <div class="option-label">Model:</div>
                <div class="option-buttons" id="model-options">
                  <label class="option-btn selected">
                    <input type="radio" name="model" value="gpt-4o" class="option-radio" checked>
                    gpt-4o
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="model" value="deepseekR1" class="option-radio">
                    deepseekR1
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="model" value="deepseekR1" class="option-radio">
                    o3-mini
                  </label>
                </div>
              </div>

              <!-- Old dropdowns - hidden but kept for compatibility -->
              <div class="custom-dropdown" id="language-dropdown" style="display: none;">
                <div class="dropdown-selected">Python</div>
                <div class="dropdown-options">
                  <div class="dropdown-option" data-value="Python">Python</div>
                  <div class="dropdown-option" data-value="JavaScript">JavaScript</div>
                  <div class="dropdown-option" data-value="Java">Java</div>
                  <div class="dropdown-option" data-value="C++">C++</div>
                </div>
              </div>

              <div class="custom-dropdown" id="model-dropdown" style="display: none;">
                <div class="dropdown-selected">gpt-4o</div>
                <div class="dropdown-options">
                  <div class="dropdown-option" data-value="gpt-4o">gpt-4o</div>
                  <div class="dropdown-option" data-value="deepseekR1">deepseekR1</div>
                </div>
              </div>
            </div>

            <div class="input-row">
              <textarea class="chat-input" id="chat-input" placeholder="Type your question here..." rows="1"></textarea>
              <button class="send-button" id="send-button">
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>

            <div class="chat-actions">
              <button class="action-btn" id="clear-chat" title="Clear Conversation">
                <i class="fas fa-trash-alt"></i> Clear
              </button>
              <button class="action-btn" id="optimize-code" title="Get Optimized Code">
                <i class="fas fa-bolt"></i> Optimize
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- General Chat Tab -->
      <div class="tab-content" id="general-tab">
        <div class="chat-interface">
          <!-- Chat Messages Area -->
          <div class="chat-messages-container">
            <div class="chat-header">
              <span class="chat-title">General Chat</span>
              <span id="general-chat-status" class="chat-status">Ready</span>
            </div>
            <div class="chat-messages" id="general-chat-messages">
              <div class="message ai-message visible">
                <div class="message-content">
                  Hi! I'm here for general conversations, mental quiz questions, trivia, or any casual chat. Feel free to ask me anything! You can also capture screenshots (Ctrl+Shift+C, then Ctrl+Shift+P) if you need help with something visual.
                </div>
                <div class="message-time">Now</div>
              </div>
            </div>
          </div>

          <!-- Chat Input Area -->
          <div class="chat-input-container">
            <div class="chat-toolbox">
              <!-- Chat Type Option Group -->
              <div class="option-group">
                <div class="option-label">Chat Type:</div>
                <div class="option-buttons" id="general-chat-type-options">
                  <label class="option-btn selected">
                    <input type="radio" name="general-chat-type" value="general" class="option-radio" checked>
                    General
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="general-chat-type" value="quiz" class="option-radio">
                    Mental Quiz
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="general-chat-type" value="trivia" class="option-radio">
                    Trivia
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="general-chat-type" value="casual" class="option-radio">
                    Casual
                  </label>
                </div>
              </div>

              <!-- Model Option Group -->
              <div class="option-group">
                <div class="option-label">Model:</div>
                <div class="option-buttons" id="general-model-options">
                  <label class="option-btn selected">
                    <input type="radio" name="general-model" value="gpt-4o" class="option-radio" checked>
                    gpt-4o
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="general-model" value="deepseekR1" class="option-radio">
                    deepseekR1
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="general-model" value="o3-mini" class="option-radio">
                    o3-mini
                  </label>
                </div>
              </div>
            </div>

            <div class="input-row">
              <textarea class="chat-input" id="general-chat-input" placeholder="Ask me anything... general questions, mental quiz, trivia, or just chat!" rows="1"></textarea>
              <button class="send-button" id="general-send-button">
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>

            <div class="chat-actions">
              <button class="action-btn" id="general-clear-chat" title="Clear Conversation">
                <i class="fas fa-trash-alt"></i> Clear
              </button>
              <button class="action-btn" id="general-capture-btn" title="Capture Screen (Ctrl+Shift+C)">
                <i class="fas fa-camera"></i> Capture
              </button>
              <button class="action-btn" id="general-process-btn" title="Process Image (Ctrl+Shift+P)">
                <i class="fas fa-cogs"></i> Process
              </button>
              <button class="action-btn" id="general-save-chat" title="Save Conversation">
                <i class="fas fa-save"></i> Save
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- System Design Tab -->
      <div class="tab-content" id="system-design-tab">
        <div class="chat-interface">
          <!-- Chat Messages Area -->
          <div class="chat-messages-container">
            <div class="chat-header">
              <span class="chat-title">System Design</span>
              <span id="system-design-status" class="chat-status">Ready</span>
            </div>
            <div class="chat-messages" id="system-design-messages">
              <div class="message ai-message visible">
                <div class="message-content">
                  Hello! I can help with system design. Describe your problem (e.g., "Design a scalable e-commerce system") or capture a screenshot (Ctrl+Shift+C, then Ctrl+Shift+P to process). I'll provide a diagram and detailed explanation.
                </div>
                <div class="message-time">Now</div>
              </div>
            </div>
          </div>

          <!-- Chat Input Area -->
          <div class="chat-input-container">
            <div class="chat-toolbox">
              <!-- Model Option Group for System Design -->
              <div class="option-group">
                <div class="option-label">Model:</div>
                <div class="option-buttons" id="system-design-model-options">
                  <label class="option-btn selected">
                    <input type="radio" name="system-design-model" value="gpt-4o" class="option-radio" checked>
                    gpt-4o
                  </label>
                  <label class="option-btn">
                    <input type="radio" name="system-design-model" value="deepseekR1" class="option-radio">
                    deepseekR1
                  </label>
                </div>
              </div>

              <!-- Old dropdown - hidden but kept for compatibility -->
              <div class="custom-dropdown" id="system-design-model-dropdown" style="display: none;">
                <div class="dropdown-selected">gpt-4o</div>
                <div class="dropdown-options">
                  <div class="dropdown-option" data-value="gpt-4o">gpt-4o</div>
                  <div class="dropdown-option" data-value="deepseekR1">deepseekR1</div>
                </div>
              </div>
            </div>

            <div class="input-row">
              <textarea class="chat-input" id="system-design-input" placeholder="Describe your system design problem..." rows="1"></textarea>
              <button class="send-button" id="system-design-send-button">
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>

            <div class="chat-actions">
              <button class="action-btn" id="system-design-clear-chat" title="Clear Conversation">
                <i class="fas fa-trash-alt"></i> Clear
              </button>
              <button class="action-btn" id="system-design-save-chat" title="Save Conversation">
                <i class="fas fa-save"></i> Save
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Audio Tab -->
      <div class="tab-content" id="audio-tab">
        <div class="audio-interface">
          <div class="audio-header">
            <span class="audio-title">Audio Recording</span>
            <span id="recording-status" class="audio-status">Ready</span>
          </div>

          <!-- <div class="audio-controls">
            <button id="startRecordingBtn" class="recording-btn" title="Start Recording (Ctrl+Shift+R)">
              <i class="fas fa-microphone"></i> Start Recording
            </button>
            <button id="stopRecordingBtn" class="recording-btn" title="Stop Recording (Ctrl+Shift+R)" disabled>
              <i class="fas fa-stop"></i> Stop Recording
            </button>
          </div> -->

          <div class="audio-content">
            <div class="audio-info" id="recording-info">
              <div class="info-placeholder">
                <i class="fas fa-info-circle"></i>
                <p>Coming soon</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Shortcuts Tab -->
      <div class="tab-content" id="shortcuts-tab">
        <div class="shortcuts-interface">
          <div class="shortcuts-header">
            <span class="shortcuts-title">Keyboard Shortcuts</span>
          </div>

          <div class="shortcuts-content" id="shortcuts-content">
            <!-- This will be populated dynamically by ui.js -->
          </div>
        </div>
      </div>
    </main>

    <!-- Status Bar -->
    <footer class="status-bar">
      <div class="status-info">
        <i class="fas fa-info-circle"></i>
        <span id="window-size"></span>
      </div>
      <div class="app-version">
        v1.0.2
      </div>
    </footer>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.0.2/marked.min.js"></script>
  <script src="ui.js"></script>
  <script src="app.js" defer></script>
</body>
</html>